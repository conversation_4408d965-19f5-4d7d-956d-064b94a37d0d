# Grok 4 Modernization Plan

## Executive Summary

This document outlines the required updates to modernize the `llm-grok` plugin to support xAI's new Grok 4 model, released in July 2025. The current implementation supports only Grok 2 and Grok 3 models with basic text-only chat completions. Grok 4 introduces significant new capabilities including multimodal support, function calling, a 256k token context window, and multiple API endpoints.

Feel free to access the cheat sheet for a quick reference: `/Users/<USER>/Documents/development/llm-grok/grok-4-cheat-sheet.md`

## Current State Analysis

### Supported Features
- Basic text chat completions via OpenAI-compatible API
- Streaming and non-streaming responses
- Temperature and max_completion_tokens parameters
- Rate limiting with exponential backoff
- Basic conversation history support
- Models: Grok 2 and Grok 3 variants

### Architecture
- Single module design (`llm_grok.py`)
- Extends LLM's `KeyModel` base class
- Uses httpx for HTTP requests
- Rich library for terminal UI

## Gap Analysis

### 1. Model Support
**Current**: Lists only Grok 2 and 3 models
**Required**: Add `x-ai/grok-4` and `grok-4-heavy`

### 2. Multimodal Capabilities
**Current**: Text-only implementation
**Required**: Image input support using OpenAI's multimodal message format

### 3. Function/Tool Calling
**Current**: Not implemented
**Required**: Full support for parallel tool calling with JSON Schema definitions

### 4. API Endpoints
**Current**: Only `/chat/completions`
**Required**: Support for `/messages` (Anthropic-style) and `/image-generations`

### 5. Advanced Parameters
**Current**: Only temperature and max_completion_tokens
**Required**: Add support for:
- `tools` (array of function definitions)
- `tool_choice` (auto or specific function)
- JSON mode/structured output
- System message caching

### 6. Context Window
**Current**: No explicit handling
**Required**: Document and validate 256k token limit

### 7. Reasoning Features
**Current**: No special handling
**Required**: Document automatic "think-before-answer" behavior

## Modernization Plan

### Phase 1: Core Model Support (Priority: High)

#### 1.1 Update Model List
```python
AVAILABLE_MODELS = [
    # Grok 4 models (new)
    "x-ai/grok-4",
    "grok-4-heavy",
    # Existing models
    "grok-3-latest",
    "grok-3-fast-latest",
    "grok-3-mini-latest",
    "grok-3-mini-fast-latest",
    "grok-2-latest",
    "grok-2-vision-latest",
]
DEFAULT_MODEL = "x-ai/grok-4"  # Update default
```

#### 1.2 Add Model Metadata
Create a model registry with capabilities:
```python
MODEL_INFO = {
    "x-ai/grok-4": {
        "context_window": 256000,
        "supports_vision": True,
        "supports_tools": True,
        "pricing_tier": "standard"
    },
    # ... other models
}
```

### Phase 2: Multimodal Support (Priority: High)

#### 2.1 Update Message Building
Enhance `build_messages()` to handle multimodal content:
```python
def build_message_content(prompt):
    if hasattr(prompt, 'attachments') and prompt.attachments:
        content = [{"type": "text", "text": prompt.prompt}]
        for attachment in prompt.attachments:
            if attachment.type == "image":
                content.append({
                    "type": "image_url",
                    "image_url": {"url": attachment.data}
                })
        return content
    return prompt.prompt
```

#### 2.2 Add Image Handling
- Support base64 encoded images
- Support image URLs
- Validate image formats

### Phase 3: Function Calling (Priority: Medium)

#### 3.1 Extend Options Class
```python
class Options(llm.Options):
    temperature: Optional[float] = Field(...)
    max_completion_tokens: Optional[int] = Field(...)
    tools: Optional[List[Dict]] = Field(
        description="List of function definitions",
        default=None
    )
    tool_choice: Optional[Union[str, Dict]] = Field(
        description="How to choose tools: 'auto' or specific function",
        default="auto"
    )
    response_format: Optional[Dict] = Field(
        description="Structured output format",
        default=None
    )
```

#### 3.2 Handle Tool Responses
- Parse tool_calls from API responses
- Support multiple parallel tool calls
- Add helper methods for tool result formatting

### Phase 4: Additional Endpoints (Priority: Low)

#### 4.1 Anthropic-style Messages Endpoint
- Add method to convert between message formats
- Support `/messages` endpoint as alternative

#### 4.2 Image Generation
- Add new command: `llm grok generate-image`
- Support DALL-E compatible parameters

### Phase 5: Enhanced Features (Priority: Medium)

#### 5.1 Add New Commands
```python
@grok.command()
def capabilities():
    """Show model capabilities and limits"""
    
@grok.command()
def pricing():
    """Show pricing information"""
```

#### 5.2 Context Window Management
- Add token counting utilities
- Warn when approaching 256k limit
- Implement message truncation strategies

#### 5.3 Structured Output Support
- Add JSON mode support
- Schema validation helpers
- Examples in documentation

### Phase 6: Testing Updates (Priority: High)

#### 6.1 New Test Cases
- Multimodal message building
- Function calling request/response
- New model IDs
- Large context handling
- Structured output parsing

#### 6.2 Mock Fixtures
- Add fixtures for Grok 4 responses
- Tool calling scenarios
- Image input examples

### Phase 7: Documentation (Priority: High)

#### 7.1 Update README
- New model information
- Multimodal examples
- Function calling guide
- Migration guide from Grok 3 to 4

#### 7.2 Add Examples
- Basic text chat
- Image analysis
- Function calling
- Structured output
- Large document processing

## Implementation Priority

1. **Immediate (Week 1)**
   - Add Grok 4 model IDs
   - Update defaults
   - Basic testing

2. **High Priority (Week 2-3)**
   - Multimodal support
   - Function calling
   - Update tests
   - Documentation

3. **Medium Priority (Week 4)**
   - Enhanced features
   - Additional commands
   - Performance optimizations

4. **Low Priority (Future)**
   - Alternative endpoints
   - Image generation
   - Advanced caching

## Breaking Changes

### Backwards Compatibility
- Maintain support for all existing models
- Keep current API intact
- Add new features via optional parameters

### Migration Path
1. Update model list to include new models
2. Existing code continues to work
3. New features opt-in via parameters
4. Deprecation warnings for old defaults (after 3 months)

## Testing Strategy

### Unit Tests
- Test each new feature in isolation
- Mock all API calls
- Cover error scenarios

### Integration Tests
- Manual testing with real API (developer key)
- Verify all model variants
- Test rate limiting behavior

### Performance Tests
- Large context handling
- Streaming performance
- Parallel tool calls

## Release Plan

### Version 2.0.0
- Full Grok 4 support
- Breaking change: new default model
- Comprehensive documentation

### Version 1.1.0 (if preferred)
- Add Grok 4 as option
- Keep existing defaults
- Feature flags for new capabilities

## Security Considerations

- Validate image inputs to prevent injection
- Sanitize tool definitions
- Rate limit compliance
- Secure key handling for new pricing tiers

## Conclusion

The modernization to support Grok 4 requires significant enhancements to handle multimodal inputs, function calling, and expanded API capabilities. However, the changes can be implemented incrementally while maintaining backwards compatibility. The single-module design of the current implementation makes it straightforward to add these features without major architectural changes.