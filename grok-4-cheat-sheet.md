# Grok 4 API & Usage Cheat Sheet (July 2025)

> **Grok 4** is xAI’s newest frontier-level model, released overnight on **9-10 July 2025**. It comes with an OpenAI-compatible REST interface, 256 k-token context, built-in multimodal reasoning and first-class function calling[1][2].

## 1. Fast Facts  

| Item | Detail |
|------|--------|
| Model ID | `x-ai/grok-4` (also appears as `grok-4` on xAI Cloud and **OpenRouter**)[3] |
| Release | 9 – 10 July 2025 livestream & docs drop[4] |
| Context Window | **256 000 tokens**[1] |
| Modalities | Text ↔ Text -  Image → Text (vision input)[1] |
| Special Features | Parallel **tool/function calling**, structured JSON output, “think-before-answer” reasoning that cannot be disabled[1][2] |
| API Style | Same request / response shapes as OpenAI & Anthropic SDKs; just swap base URL & model name[5] |

## 2. Base URL & Endpoints  

| Purpose | Path | Notes |
|---------|------|-------|
| Chat completions (OpenAI-style) | `POST /chat/completions` | Primary text & multimodal endpoint[6] |
| Messages (Anthropic-style) | `POST /messages` | Accepts Claude-style messages[6] |
| Image generations | `POST /image-generations` | DALL-E-like creative images[6] |
| List models | `GET /models` | Returns Grok 4, Grok 4 Heavy, etc.[6] |

_OpenRouter mirror:_ `https://openrouter.ai/api/v1/chat/completions`[3]

## 3. Authentication & Required Headers  

```http
Authorization: Bearer YOUR_API_KEY           # xAI key or OpenRouter key
Content-Type: application/json
HTTP-Referer: https://your-app.example.com    # OpenRouter only[6]
```

## 4. Minimal Chat Request (cURL)

```bash
curl -L https://openrouter.ai/api/v1/chat/completions \
 -H "Authorization: Bearer $OR_KEY" \
 -H "Content-Type: application/json" \
 -H "HTTP-Referer: https://myapp.dev" \
 -d '{
   "model": "x-ai/grok-4",
   "messages": [
     {"role":"user","content":"Explain quantum computing in plain English."}
   ],
   "temperature": 0.7,
   "stream": true
 }'
```
The streamed response follows OpenAI’s SSE chunk format with `choices.delta.content` updates[3].

## 5. Parameter Cheat-Sheet  

| Field | Type | Purpose |
|-------|------|---------|
| `model` | string | Must be `x-ai/grok-4` (or `...-heavy`) [3] |
| `messages` | array | Conversation history (OpenAI roles) |
| `temperature` | float 0-2 | Higher → more creative[3] |
| `max_tokens` | int | Hard cap on response length[3] |
| `stream` | bool | `true` for server-sent chunks[3] |
| `tools` | array | JSON-Schema definitions for function calling[2] |
| `tool_choice` | `"auto"`\|name | Let Grok pick the right function or force one[2] |

## 6. Function / Tool Calling

1. **Describe every function** clearly; include `name`, `description`, and a JSON-Schema `parameters` object[2].  
2. Send them in the `tools` list with each chat request.  
3. Grok may return one **or several** `tool_calls` in a single response; execute in order received[1].  
4. Return the tool results back to the model as a new `assistant` message with `role:"tool"` to finish the turn.

## 7. Multimodal Usage (Image + Text)

```json
"messages": [
  {
    "role": "user",
    "content": [
      {"type":"text","text":"What’s in this photo?"},
      {"type":"image_url","image_url":{"url":"data:image/png;base64,iVBORw0KG..." }}
    ]
  }
]
```
Grok returns the analysis in the normal assistant message; no extra flags needed[1].

## 8. Limits & Quotas  

| Capability | Limit |
|------------|-------|
| Context window | 256 k tokens per request[1] |
| Parallel tools | Multiple per turn (no official cap)[1] |
| Rate limits | Subject to plan; respect 429 `rate_limit_exceeded`[3] |

## 9. Pricing & Access Tiers  

| Plan | Monthly | Includes |
|------|---------|----------|
| Grok 4 (consumer) | **$30** | Standard model via API & X app[7] |
| **SuperGrok Heavy** | **$300** | Grok 4 Heavy + early-access features[4] |

_Token-based billing for API calls (exact per-token $ pending); OpenRouter passes through its own rates[3]._

## 10. Best-Practice Tips  

- **Cache** prompts or system messages to cut total tokens and cost[2].  
- Give **explicit schemas** when you need JSON, CSV, or other structured output[2].  
- For deterministic answers use `temperature:0` and raise `top_p` sparingly.  
- Supply **step-by-step instructions** if you want Grok’s internal reasoning surfaced in the reply (“Show your reasoning…”)—it performs chain-of-thought automatically but will redact it unless asked[2].  
- When chaining tools, keep each function **idempotent**; Grok may retry calls.  
- Trim large PDFs or chats; Grok’s 256 k tokens cover ≈ 200k English words, but longer inputs **slow latency**.

## 11. Error Reference (common)

| HTTP | Code | Meaning |
|------|------|---------|
| 400 | `invalid_request_error` | Bad JSON / missing field |
| 401 | `authentication_error` | Key missing / revoked |
| 429 | `rate_limit_exceeded` | Too many requests[3] |
| 500 | `server_error` | Temporary xAI issue |

## 12. Quick Migration Snippet (Python, OpenAI SDK)

```python
import openai

openai.api_key = "xai_sk_..."          # Your xAI key
openai.api_base = "https://api.x.ai/v1"

response = openai.ChatCompletion.create(
    model="x-ai/grok-4",
    messages=[{"role":"user","content":"Summarize the new EU AI Act."}]
)
print(response.choices[0].message.content)
```
xAI’s REST surface is intentionally drop-in compatible with the OpenAI client[5].

### You are now ready to Grok 4 🚀 — happy building!

[1] https://openrouter.ai/x-ai/grok-4/api
[2] https://apidog.com/blog/how-to-access-grok-4-api
[3] https://apidog.com/blog/grok-4-api-openrouter/
[4] https://www.teslarati.com/elon-musk-confirms-grok-4-launch-july-9-livestream-event/
[5] https://x.ai/api
[6] https://docs.x.ai/docs/tutorial
[7] https://www.wired.com/story/grok-4-elon-musk-xai-antisemitic-posts/
[8] https://docs.x.ai/docs/models/grok-4-0709
[9] https://www.appaca.ai/resources/ai-models/grok-api-documentation-overview
[10] https://yourgpt.ai/blog/updates/grok-4
[11] https://dorik.com/blog/how-to-use-grok-ai
[12] https://techcrunch.com/2025/07/09/elon-musks-xai-launches-grok-4-alongside-a-300-monthly-subscription/
[13] https://www.ai-mindset.ai/grok-cheatsheet
[14] https://www.tomsguide.com/ai/grok-4-is-here-elon-musk-says-its-the-same-model-physicists-use
[15] https://www.byteplus.com/en/topic/513370
[16] https://latenode.com/blog/complete-guide-to-xais-grok-api-documentation-and-implementation
[17] https://groklearning.com/a/resources/python-cheatsheet-poster/
[18] https://www.cbsnews.com/news/elon-musk-grok-4-ai-chatbot-x/
[19] https://x.com/xai/status/1943158495588815072
[20] https://www.axios.com/2025/06/04/ai-cheat-sheet-chatgpt-grok-gemini-claude
[21] https://www.reuters.com/business/autos-transportation/grok-ai-be-available-tesla-vehicles-next-week-musk-says-2025-07-10/